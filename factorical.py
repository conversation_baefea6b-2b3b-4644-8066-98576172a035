# Program to implement Permutation and Combination formulas
# Permutation: P(n,r) = n!/(n-r)!
# Combination: C(n,r) = n!/(r!*(n-r)!) = P(n,r)/r!

def factorial(n):
    """
    Calculate factorial of a number
    Returns n! = n * (n-1) * (n-2) * ... * 1
    """
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    if n == 0 or n == 1:
        return 1

    result = 1
    for i in range(2, n + 1):
        result *= i
    return result

def permutation(n, r):
    """
    Calculate permutation P(n,r) = n!/(n-r)!
    Number of ways to arrange r objects from n objects where order matters
    """
    if n < 0 or r < 0:
        raise ValueError("n and r must be non-negative")
    if r > n:
        return 0  # Cannot select more items than available

    # Using the formula P(n,r) = n!/(n-r)!
    return factorial(n) // factorial(n - r)

def combination(n, r):
    """
    Calculate combination C(n,r) = n!/(r!*(n-r)!)
    Number of ways to choose r objects from n objects where order doesn't matter
    """
    if n < 0 or r < 0:
        raise ValueError("n and r must be non-negative")
    if r > n:
        return 0  # Cannot select more items than available

    # Using the formula C(n,r) = n!/(r!*(n-r)!)
    return factorial(n) // (factorial(r) * factorial(n - r))

def permutation_efficient(n, r):
    """
    More efficient calculation of permutation using direct multiplication
    P(n,r) = n * (n-1) * (n-2) * ... * (n-r+1)
    """
    if n < 0 or r < 0:
        raise ValueError("n and r must be non-negative")
    if r > n:
        return 0

    result = 1
    for i in range(n, n - r, -1):
        result *= i
    return result

def combination_efficient(n, r):
    """
    More efficient calculation of combination
    C(n,r) = P(n,r) / r!
    """
    if n < 0 or r < 0:
        raise ValueError("n and r must be non-negative")
    if r > n:
        return 0

    # Use the smaller value to reduce calculations
    if r > n - r:
        r = n - r

    return permutation_efficient(n, r) // factorial(r)

def display_formulas():
    """
    Display the mathematical formulas
    """
    print("PERMUTATION AND COMBINATION FORMULAS")
    print("=" * 40)
    print("Permutation P(n,r) = n!/(n-r)!")
    print("  - Number of ways to arrange r objects from n objects")
    print("  - Order matters")
    print()
    print("Combination C(n,r) = n!/(r!*(n-r)!) = P(n,r)/r!")
    print("  - Number of ways to choose r objects from n objects")
    print("  - Order doesn't matter")
    print()

def calculate_examples():
    """
    Calculate and display some examples
    """
    print("EXAMPLES:")
    print("-" * 20)

    examples = [
        (5, 3),
        (10, 4),
        (8, 2),
        (6, 6),
        (7, 0)
    ]

    for n, r in examples:
        p = permutation(n, r)
        c = combination(n, r)
        print(f"n={n}, r={r}:")
        print(f"  P({n},{r}) = {n}!/({n}-{r})! = {p}")
        print(f"  C({n},{r}) = {n}!/({r}!*({n}-{r})!) = {c}")
        print()

def main():
    """
    Main function to demonstrate permutation and combination calculations
    """
    display_formulas()
    calculate_examples()

    # Interactive calculation
    print("INTERACTIVE CALCULATOR:")
    print("-" * 25)

    while True:
        try:
            print("\nEnter values for calculation (or 'q' to quit):")
            n_input = input("Enter n (total objects): ")
            if n_input.lower() == 'q':
                break

            r_input = input("Enter r (objects to select): ")
            if r_input.lower() == 'q':
                break

            n = int(n_input)
            r = int(r_input)

            if n < 0 or r < 0:
                print("Please enter non-negative numbers.")
                continue

            p = permutation(n, r)
            c = combination(n, r)

            print(f"\nResults for n={n}, r={r}:")
            print(f"Permutation P({n},{r}) = {p}")
            print(f"Combination C({n},{r}) = {c}")

            if r <= n and r > 0:
                print(f"\nVerification:")
                print(f"P({n},{r}) = {n}!/({n-r})! = {factorial(n)}/{factorial(n-r)} = {p}")
                print(f"C({n},{r}) = P({n},{r})/r! = {p}/{factorial(r)} = {c}")

        except ValueError as e:
            print(f"Error: {e}")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break

# Run the program
if __name__ == "__main__":
    main()
import mat