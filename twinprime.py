# Program to print twin primes less than 1000
# Twin primes: Two consecutive odd numbers that are both prime


def is_prime(n):
    
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False

    # Check odd divisors up to sqrt(n)
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return False
    return True

def find_twin_primes(limit):
    """
    Function to find all twin primes less than the given limit
    Returns a list of twin prime pairs
    """
    twin_primes = []

    # Start from 3 (first odd prime) and check consecutive odd numbers
    for i in range(3, limit - 2, 2):  # Step by 2 to get only odd numbers
        if is_prime(i) and is_prime(i + 2):
            twin_primes.append((i, i + 2))

    return twin_primes

def main():
    """
    Main function to find and display twin primes less than 1000
    """
    limit = 1000
    print(f"Twin Primes less than {limit}:")
    print("=" * 30)

    twin_primes = find_twin_primes(limit)

    # Display the twin primes
    for i, (p1, p2) in enumerate(twin_primes, 1):
        print(f"{i:2d}. ({p1}, {p2})")

    print(f"\nTotal twin prime pairs found: {len(twin_primes)}")

# Run the program
if __name__ == "__main__":
    main()
